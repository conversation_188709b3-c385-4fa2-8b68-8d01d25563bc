import type { Voice } from "@/types/tts"

export const VOICES: Voice[] = [
  // English Voices
  {
    id: "en-deep-man",
    name: "Deep Voice Man",
    language: "en",
    gender: "male",
    sampleUrl: "English_ManWithDeepVoice",
  },
  {
    id: "en-calm-woman",
    name: "Calm Woman",
    language: "en",
    gender: "female",
    sampleUrl: "English_CalmWoman",
  },
  {
    id: "en-trustworthy-man",
    name: "Trustworthy Man",
    language: "en",
    gender: "male",
    sampleUrl: "English_Trustworth_Man",
  },
  {
    id: "en-graceful-lady",
    name: "Graceful Lady",
    language: "en",
    gender: "female",
    sampleUrl: "English_Graceful_Lady",
  },
  {
    id: "en-gentle-man",
    name: "Gentle-voiced Man",
    language: "en",
    gender: "male",
    sampleUrl: "English_Gentle-voiced_man",
  },
  {
    id: "en-lovely-girl",
    name: "Lovely Girl",
    language: "en",
    gender: "female",
    sampleUrl: "English_LovelyGirl",
  },
  {
    id: "en-wise-lady",
    name: "Wise Lady",
    language: "en",
    gender: "female",
    sampleUrl: "English_Wiselady",
  },
  {
    id: "en-storyteller",
    name: "Captivating Storyteller",
    language: "en",
    gender: "male",
    sampleUrl: "English_CaptivatingStoryteller",
  },
  {
    id: "en-confident-woman",
    name: "Confident Woman",
    language: "en",
    gender: "female",
    sampleUrl: "English_ConfidentWoman",
  },
  {
    id: "en-aussie-bloke",
    name: "Aussie Bloke",
    language: "en",
    gender: "male",
    sampleUrl: "English_Aussie_Bloke",
  },

  // Chinese Voices
  {
    id: "zh-reliable-executive",
    name: "可靠主管",
    language: "zh-cn",
    gender: "male",
    sampleUrl: "Chinese (Mandarin)_Reliable_Executive",
  },
  {
    id: "zh-warm-girl",
    name: "温暖女孩",
    language: "zh-cn",
    gender: "female",
    sampleUrl: "Chinese (Mandarin)_Warm_Girl",
  },
  {
    id: "zh-news-anchor",
    name: "新闻主播",
    language: "zh-cn",
    gender: "male",
    sampleUrl: "Chinese (Mandarin)_News_Anchor",
  },
  {
    id: "zh-mature-woman",
    name: "成熟女性",
    language: "zh-cn",
    gender: "female",
    sampleUrl: "Chinese (Mandarin)_Mature_Woman",
  },
  {
    id: "zh-gentleman",
    name: "绅士",
    language: "zh-cn",
    gender: "male",
    sampleUrl: "Chinese (Mandarin)_Gentleman",
  },
  {
    id: "zh-sweet-lady",
    name: "甜美女士",
    language: "zh-cn",
    gender: "female",
    sampleUrl: "Chinese (Mandarin)_Sweet_Lady",
  },
  {
    id: "zh-warm-bestie",
    name: "温暖闺蜜",
    language: "zh-cn",
    gender: "female",
    sampleUrl: "Chinese (Mandarin)_Warm_Bestie",
  },
  {
    id: "zh-radio-host",
    name: "电台主持",
    language: "zh-cn",
    gender: "male",
    sampleUrl: "Chinese (Mandarin)_Radio_Host",
  },

  // Japanese Voices
  {
    id: "ja-intellectual-senior",
    name: "知的な先輩",
    language: "ja",
    gender: "male",
    sampleUrl: "Japanese_IntellectualSenior",
  },
  {
    id: "ja-decisive-princess",
    name: "決断力のある姫",
    language: "ja",
    gender: "female",
    sampleUrl: "Japanese_DecisivePrincess",
  },
  {
    id: "ja-gentle-butler",
    name: "優しい執事",
    language: "ja",
    gender: "male",
    sampleUrl: "Japanese_GentleButler",
  },
  {
    id: "ja-kind-lady",
    name: "優しい女性",
    language: "ja",
    gender: "female",
    sampleUrl: "Japanese_KindLady",
  },

  // Korean Voices
  {
    id: "ko-sweet-girl",
    name: "달콤한 소녀",
    language: "ko",
    gender: "female",
    sampleUrl: "Korean_SweetGirl",
  },
  {
    id: "ko-cheerful-boyfriend",
    name: "쾌활한 남자친구",
    language: "ko",
    gender: "male",
    sampleUrl: "Korean_CheerfulBoyfriend",
  },
  {
    id: "ko-elegant-princess",
    name: "우아한 공주",
    language: "ko",
    gender: "female",
    sampleUrl: "Korean_ElegantPrincess",
  },
  {
    id: "ko-calm-gentleman",
    name: "차분한 신사",
    language: "ko",
    gender: "male",
    sampleUrl: "Korean_CalmGentleman",
  },
]

export const LANGUAGE_NAMES: Record<string, string> = {
  en: "EN",
  "zh-cn": "中文",
  es: "ES",
  fr: "FR",
  de: "DE",
  ja: "日本語",
  ko: "한국어",
}
