import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function detectLanguage(text: string): string {
  // 简单的语言检测逻辑
  const chineseRegex = /[\u4e00-\u9fff]/
  const japaneseRegex = /[\u3040-\u309f\u30a0-\u30ff]/
  const koreanRegex = /[\uac00-\ud7af]/

  if (chineseRegex.test(text)) return "zh-cn"
  if (japaneseRegex.test(text)) return "ja"
  if (koreanRegex.test(text)) return "ko"

  return "en" // 默认英语
}

export function generateUUID(): string {
  return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0
    const v = c == "x" ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}

export function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 Bytes"
  const k = 1024
  const sizes = ["Bytes", "KB", "MB", "GB"]
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
}

export const LANGUAGE_NAMES: Record<string, string> = {
  en: "EN",
  "zh-cn": "中文",
  es: "ES",
  fr: "FR",
  de: "DE",
  ja: "日本語",
  ko: "한국어",
}
