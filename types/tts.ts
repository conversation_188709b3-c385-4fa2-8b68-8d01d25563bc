export interface TTSRequest {
  text: string
  language: string
  voice: string
  speed: number
  pitch: number
}

export interface TTSResponse {
  jobId: string
  audioUrl?: string
  status: "pending" | "processing" | "completed" | "failed"
}

export interface Voice {
  id: string
  name: string
  language: string
  gender: "male" | "female"
  sampleUrl: string
}

export interface LanguageDetection {
  language: string
  confidence: number
  code: string
}
