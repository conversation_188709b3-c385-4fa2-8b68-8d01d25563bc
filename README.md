# TTS App - AI Text-to-Speech Application

A modern, responsive text-to-speech application built with Next.js 15 and powered by Replicate's XTTS-v2 AI model.

## ✨ Features

- 🎯 **High-Quality Speech Synthesis** - Generate natural-sounding voices using XTTS-v2 AI model
- 🌍 **Multi-Language Support** - Supports Chinese, English, Japanese, Korean with automatic language detection
- 🎛️ **Voice Customization** - Multiple voice options (male/female) for different languages
- ⚡ **Real-time Processing** - Fast synthesis with real-time progress tracking
- 🎵 **Audio Controls** - Play, pause, download generated audio files
- 📱 **Responsive Design** - Works seamlessly on desktop and mobile devices
- 🎨 **Modern UI** - Beautiful glass-morphism design with smooth animations

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ 
- pnpm (recommended) or npm
- Replicate API account

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd tts-app
   ```

2. **Install dependencies**
   ```bash
   pnpm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```
   
   Edit `.env.local` and add your Replicate API token:
   ```env
   REPLICATE_API_TOKEN="your_replicate_api_token_here"
   ```
   
   Get your API token from: https://replicate.com/account/api-tokens

4. **Run the development server**
   ```bash
   pnpm dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🛠️ Tech Stack

- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS, shadcn/ui components
- **Animations**: Framer Motion
- **AI Service**: Replicate (XTTS-v2 model)
- **Package Manager**: pnpm

## 📁 Project Structure

```
├── app/                    # Next.js App Router
│   ├── page.tsx           # Landing page
│   ├── synth/page.tsx     # Synthesis page
│   ├── api/tts/           # API routes
│   └── globals.css        # Global styles
├── components/            # Reusable UI components
├── lib/                   # Utility functions
├── types/                 # TypeScript type definitions
├── public/voices/         # Voice sample files
└── README.md
```

## 🔧 Configuration

### Voice Files

The app includes sample voice files in `public/voices/`:
- `en_male.wav` - English male voice
- `en_female.wav` - English female voice  
- `zh_male.wav` - Chinese male voice
- `zh_female.wav` - Chinese female voice

You can replace these with your own voice samples for better results.

### Supported Languages

- English (en)
- Chinese (zh-cn)
- Japanese (ja)
- Korean (ko)

## 🚨 Troubleshooting

### Common Issues

1. **API Authentication Error**
   - Make sure your `REPLICATE_API_TOKEN` is set correctly in `.env.local`
   - Verify your token is valid at https://replicate.com/account/api-tokens

2. **Voice File Not Found**
   - Ensure voice files exist in `public/voices/` directory
   - Check file permissions and naming

3. **Synthesis Fails**
   - Check your Replicate account quota
   - Verify network connectivity
   - Check browser console for detailed error messages

## 📝 Usage

1. **Enter Text**: Type or paste text on the landing page
2. **Language Detection**: The app automatically detects the language
3. **Customize**: Choose voice, adjust speed and pitch on the synthesis page
4. **Generate**: Click "Generate Speech" to create audio
5. **Play & Download**: Use the audio controls to play or download the result

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📄 License

This project is open source and available under the [MIT License](LICENSE).
