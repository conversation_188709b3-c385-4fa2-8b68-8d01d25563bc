"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Card } from "@/components/ui/card"
import { Volume2, Zap, Globe, Github, Twitter, Languages, Mic, Download, Upload, ArrowRight, CheckCircle, Clock, Shield } from "lucide-react"
import { detectLanguage, LANGUAGE_NAMES } from "@/lib/utils"
import { useRouter } from "next/navigation"

export default function LandingPage() {
  const [text, setText] = useState("")
  const [detectedLanguage, setDetectedLanguage] = useState<string | null>(null)
  const router = useRouter()

  const handleTextChange = (value: string) => {
    setText(value)
    if (value.trim()) {
      const lang = detectLanguage(value)
      setDetectedLanguage(lang)
      // 保存到localStorage
      localStorage.setItem("tts-text", value)
      localStorage.setItem("tts-language", lang)
    } else {
      setDetectedLanguage(null)
    }
  }

  const handleTryIt = () => {
    if (text.trim()) {
      router.push("/synth")
    }
  }

  const features = [
    {
      icon: <Zap className="h-6 w-6 text-blue-600" />,
      title: "AI-Powered Synthesis",
      description: "Generate natural-sounding voices using advanced MiniMax Speech-02-Turbo AI model with 100% accuracy."
    },
    {
      icon: <Globe className="h-6 w-6 text-blue-600" />,
      title: "Multi-Language Support",
      description: "Supports Chinese, English, Japanese, Korean with automatic language detection and native pronunciations."
    },
    {
      icon: <Volume2 className="h-6 w-6 text-blue-600" />,
      title: "Studio Quality Audio",
      description: "Professional-grade MP3 output with customizable emotions, speed, and pitch control for any project."
    },
    {
      icon: <Clock className="h-6 w-6 text-blue-600" />,
      title: "Lightning Fast",
      description: "Convert text to speech in seconds with real-time processing and instant download capabilities."
    },
    {
      icon: <Mic className="h-6 w-6 text-blue-600" />,
      title: "Voice Customization",
      description: "Multiple voice options (male/female) for different languages with emotion and tone control."
    },
    {
      icon: <Shield className="h-6 w-6 text-blue-600" />,
      title: "Privacy Protected",
      description: "Your text data is processed securely and not stored. Complete privacy protection for your content."
    }
  ]

  const steps = [
    { step: "1", title: "Enter Text", description: "Type or paste your text content" },
    { step: "2", title: "Choose Voice", description: "Select language and voice options" },
    { step: "3", title: "Generate", description: "Click convert to create audio" },
    { step: "4", title: "Download", description: "Save your MP3 audio file" }
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Volume2 className="h-8 w-8 text-blue-600" />
            <span className="text-xl font-bold text-gray-900">Text to Speech</span>
          </div>
          <Badge variant="outline" className="border-green-500 text-green-600 bg-green-50">
            Free to Use
          </Badge>
        </div>
      </header>

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 to-indigo-100 py-16">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
                Text to Speech Converter
              </h1>
              <p className="text-xl md:text-2xl text-gray-600 mb-8">
                An online AI-powered text-to-speech converter to generate natural voices from text.
              </p>
            </motion.div>

            {/* Main Input Area */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="max-w-3xl mx-auto"
            >
              <Card className="p-8 bg-white shadow-lg border-0">
                <div className="upload-area mb-6">
                  <Upload className="h-12 w-12 text-blue-400 mx-auto mb-4" />
                  <p className="text-lg font-medium text-gray-700 mb-2">Drop, Upload or Paste Text</p>
                  <p className="text-sm text-gray-500 mb-4">Supported languages: Chinese, English, Japanese, Korean</p>
                </div>

                <div className="space-y-4">
                  <div className="relative">
                    <Textarea
                      placeholder="Type or paste your text here..."
                      value={text}
                      onChange={(e) => handleTextChange(e.target.value)}
                      className="min-h-[150px] resize-none text-base border-gray-300 focus:border-blue-500 focus:ring-blue-500 bg-white text-gray-900 placeholder:text-gray-500"
                      maxLength={5000}
                    />
                    {detectedLanguage && (
                      <motion.div
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        className="absolute top-3 right-3"
                      >
                        <Badge className="bg-blue-100 text-blue-700 border-blue-200">
                          <Languages className="h-3 w-3 mr-1" />
                          {LANGUAGE_NAMES[detectedLanguage] || detectedLanguage.toUpperCase()}
                        </Badge>
                      </motion.div>
                    )}
                  </div>

                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <span>Characters: {text.length}/5000</span>
                    <span>*Your privacy is protected! No data is stored.</span>
                  </div>

                  <Button
                    onClick={handleTryIt}
                    disabled={!text.trim()}
                    className="w-full h-14 text-lg font-semibold bg-blue-600 hover:bg-blue-700 text-white"
                  >
                    <Volume2 className="h-5 w-5 mr-2" />
                    Convert to Speech
                    <ArrowRight className="h-5 w-5 ml-2" />
                  </Button>
                </div>
              </Card>
            </motion.div>
          </div>
        </div>
      </section>

      {/* How it works */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              How does Text to Speech converter work?
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              You don't have to do much to convert text to speech if you don't know how. Simply follow these steps.
            </p>
          </div>

          <div className="grid md:grid-cols-4 gap-8 max-w-4xl mx-auto">
            {steps.map((step, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center text-xl font-bold mx-auto mb-4">
                  {step.step}
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">{step.title}</h3>
                <p className="text-gray-600 text-sm">{step.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Features */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Features - Text to Speech Converter
            </h2>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="feature-card"
              >
                <div className="mb-4">{feature.icon}</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-3">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 py-8">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center justify-between gap-4">
            <div className="flex items-center gap-2">
              <Volume2 className="h-6 w-6 text-blue-600" />
              <span className="font-semibold text-gray-900">Text to Speech</span>
            </div>
            <p className="text-gray-600 text-sm">
              We present an online AI-powered text-to-speech service to generate natural voices from text.
            </p>
            <div className="flex items-center gap-4">
              <Button variant="ghost" size="sm" className="text-gray-600 hover:text-blue-600">
                <Github className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" className="text-gray-600 hover:text-blue-600">
                <Twitter className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
