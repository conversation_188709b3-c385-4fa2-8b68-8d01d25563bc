import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import "./globals.css"
import { ThemeProvider } from "@/components/theme-provider"

export const metadata: Metadata = {
  title: "TTS - Text to Speech AI",
  description: "Transform text to natural-sounding speech with AI",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body>
        <ThemeProvider defaultTheme="dark" storageKey="tts-theme">
          {children}
        </ThemeProvider>
      </body>
    </html>
  )
}
