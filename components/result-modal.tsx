"use client"

import { motion, AnimatePresence } from "framer-motion"
import { Button } from "@/components/ui/button"
import { <PERSON><PERSON>, RotateCcw, X } from "lucide-react"
import { formatFileSize } from "@/lib/utils"

interface ResultModalProps {
  isOpen: boolean
  onClose: () => void
  audioUrl: string
  fileSize: number
  onCopyLink: () => void
  onGenerateAnother: () => void
}

export function ResultModal({ isOpen, onClose, audioUrl, fileSize, onCopyLink, onGenerateAnother }: ResultModalProps) {
  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          onClick={onClose}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            transition={{ type: "spring", duration: 0.5 }}
            className="glass-card p-6 max-w-md w-full"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Audio Generated!</h3>
              <Button variant="ghost" size="sm" onClick={onClose}>
                <X className="h-4 w-4" />
              </Button>
            </div>

            <div className="space-y-4">
              {/* Waveform Thumbnail Placeholder */}
              <div className="h-20 bg-gradient-to-r from-brand-from to-brand-to rounded-lg flex items-center justify-center">
                <div className="flex items-center gap-1">
                  {Array.from({ length: 20 }).map((_, i) => (
                    <div
                      key={i}
                      className="w-1 bg-white/60 rounded-full"
                      style={{ height: `${Math.random() * 40 + 10}px` }}
                    />
                  ))}
                </div>
              </div>

              <div className="text-center text-sm text-muted-foreground">File size: {formatFileSize(fileSize)}</div>

              <div className="flex gap-2">
                <Button variant="outline" className="flex-1" onClick={onCopyLink}>
                  <Copy className="h-4 w-4 mr-2" />
                  Copy Link
                </Button>
                <Button className="flex-1 brand-gradient text-white" onClick={onGenerateAnother}>
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Generate Another
                </Button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}
