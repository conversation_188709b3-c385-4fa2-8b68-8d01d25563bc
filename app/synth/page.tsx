"use client"

import { useState, useEffect, useRef } from "react"
import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Slider } from "@/components/ui/slider"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Progress } from "@/components/ui/progress"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Play, Pause, Download, ArrowLeft, Languages } from "lucide-react"
import { VOICES } from "@/lib/voices"
import { ErrorToast } from "@/components/error-toast"
import { ResultModal } from "@/components/result-modal"
import { generateUUID, detectLanguage, LANGUAGE_NAMES } from "@/lib/utils"
import { useRouter } from "next/navigation"

export default function SynthPage() {
  const router = useRouter()
  const [text, setText] = useState("")
  const [language, setLanguage] = useState("en")
  const [detectedLanguage, setDetectedLanguage] = useState<string | null>(null)
  const [selectedVoice, setSelectedVoice] = useState(VOICES[0].id)
  const [selectedEmotion, setSelectedEmotion] = useState("neutral")
  const [speed, setSpeed] = useState([1.0])
  const [pitch, setPitch] = useState([0])
  const [isPlaying, setIsPlaying] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [progress, setProgress] = useState(0)
  const [audioUrl, setAudioUrl] = useState<string | null>(null)
  const [audioFileSize, setAudioFileSize] = useState<number>(0)
  const [error, setError] = useState<string | null>(null)
  const [showResultModal, setShowResultModal] = useState(false)
  const audioRef = useRef<HTMLAudioElement>(null)

  useEffect(() => {
    // 从localStorage加载数据
    const savedText = localStorage.getItem("tts-text")
    const savedLanguage = localStorage.getItem("tts-language")
    if (savedText) {
      setText(savedText)
      const lang = detectLanguage(savedText)
      setDetectedLanguage(lang)
      setLanguage(lang)
    }
    if (savedLanguage) setLanguage(savedLanguage)
  }, [])

  const handleTextChange = (value: string) => {
    setText(value)
    if (value.trim()) {
      const lang = detectLanguage(value)
      setDetectedLanguage(lang)
      setLanguage(lang)
      // 保存到localStorage
      localStorage.setItem("tts-text", value)
      localStorage.setItem("tts-language", lang)
    } else {
      setDetectedLanguage(null)
    }
  }

  const handleSynthesize = async () => {
    if (!text.trim()) return

    setIsLoading(true)
    setProgress(10) // 开始请求
    setError(null)

    try {
      const selectedVoiceData = VOICES.find((v) => v.id === selectedVoice)

      console.log("Sending synthesis request:", {
        text: text.trim(),
        language: language,
        voiceId: selectedVoiceData?.sampleUrl,
        emotion: selectedEmotion,
        speed: speed[0],
      })

      setProgress(30) // 发送请求

      const response = await fetch("/api/tts/synthesize", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          text: text.trim(),
          language: language,
          voiceId: selectedVoiceData?.sampleUrl,
          emotion: selectedEmotion,
          speed: speed[0],
        }),
      })

      setProgress(60) // 收到响应

      console.log("Response status:", response.status)

      if (!response.ok) {
        // 先尝试解析JSON格式的错误
        const errorData = await response.json().catch(() => ({ error: `HTTP ${response.status}: ${response.statusText}` }))
        throw new Error(errorData.error || "Unknown error")
      }

      const result = await response.json()
      const url = result.url;

      setProgress(80) // 解析响应

      console.log("Audio URL from backend:", url)

      if (!url) {
        throw new Error("Backend did not return an audio URL")
      }

      setAudioUrl(url)
      setProgress(100) // 完成

      // 获取文件大小
      try {
        const headResponse = await fetch(url, { method: 'HEAD' })
        const contentLength = headResponse.headers.get('content-length')
        if (contentLength) {
          setAudioFileSize(parseInt(contentLength))
        }
      } catch (sizeError) {
        console.warn("Failed to get file size:", sizeError)
        setAudioFileSize(1024 * 1024) // 默认1MB
      }

      // Auto-play
      if (audioRef.current) {
        audioRef.current.src = url
        try {
          await audioRef.current.play()
          setIsPlaying(true)
        } catch (playError) {
          console.warn("Auto-play failed:", playError)
          // Auto-play failed, but that's okay
        }
      }
    } catch (err) {
      console.error("Synthesis error:", err)
      const errorMessage = err instanceof Error ? err.message : "Something went wrong, please retry."
      setError(errorMessage)
    } finally {
      setIsLoading(false)
      // 保持进度条显示一会儿再清除
      setTimeout(() => setProgress(0), 1000)
    }
  }

  const handlePlayPause = () => {
    if (!audioRef.current || !audioUrl) return

    if (isPlaying) {
      audioRef.current.pause()
    } else {
      audioRef.current.play()
    }
    setIsPlaying(!isPlaying)
  }

  const handleDownload = () => {
    if (!audioUrl) return

    const link = document.createElement("a")
    link.href = audioUrl
    link.download = `tts-${generateUUID()}.mp3` // 文件格式现在是 mp3
    link.click()

    setShowResultModal(true)
  }

  const handleCopyLink = () => {
    if (audioUrl) {
      navigator.clipboard.writeText(audioUrl)
    }
  }

  const handleGenerateAnother = () => {
    setShowResultModal(false)
    setText("")
    setAudioUrl(null)
    setAudioFileSize(0)
    setIsPlaying(false)
  }

  // 真实进度跟踪 - 不需要模拟进度了

  const remainingChars = 5000 - text.length

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Sticky Top Bar */}
      <div className="sticky top-0 z-40 bg-white/95 backdrop-blur-md border-b border-gray-200 shadow-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button variant="ghost" size="sm" onClick={() => router.push("/")} className="text-gray-600 hover:text-blue-600">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
              <h1 className="text-xl font-semibold text-gray-900">Text to Speech Synthesis</h1>
            </div>
            <div className="text-sm text-gray-500">{remainingChars} characters remaining</div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="grid lg:grid-cols-[1fr_400px] gap-8">
          {/* Left Column - Waveform */}
          <div className="space-y-6">
            <Card className="bg-white border border-gray-200 shadow-sm p-6">
              <div className="h-[260px] bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg flex items-center justify-center border border-gray-100">
                {audioUrl ? (
                  <div className="flex items-center gap-2">
                    {Array.from({ length: 50 }).map((_, i) => (
                      <motion.div
                        key={i}
                        className="w-1 bg-blue-500 rounded-full"
                        style={{ height: `${Math.random() * 80 + 20}px` }}
                        animate={
                          isPlaying
                            ? {
                                scaleY: [1, 1.5, 1],
                                opacity: [0.6, 1, 0.6],
                              }
                            : {}
                        }
                        transition={{
                          duration: 0.5,
                          repeat: isPlaying ? Number.POSITIVE_INFINITY : 0,
                          delay: i * 0.05,
                        }}
                      />
                    ))}
                  </div>
                ) : (
                  <div className="text-center text-gray-500">
                    <div className="text-4xl mb-2">🎵</div>
                    <p>Audio waveform will appear here after synthesis</p>
                  </div>
                )}
              </div>
            </Card>

            {/* Progress Bar */}
            {isLoading && (
              <Card className="bg-white border border-gray-200 shadow-sm p-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm text-gray-600">
                    <span>Synthesizing audio...</span>
                    <span>{Math.round(progress)}%</span>
                  </div>
                  <Progress value={progress} className="h-2" />
                </div>
              </Card>
            )}
          </div>

          {/* Right Column - Controls */}
          <div className="space-y-6">
            {/* Text Input Card */}
            <Card className="bg-white border border-gray-200 shadow-sm p-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium text-gray-700">Text to Synthesize</label>
                  {detectedLanguage && (
                    <Badge className="bg-blue-100 text-blue-700 border-blue-200">
                      <Languages className="h-3 w-3 mr-1" />
                      {LANGUAGE_NAMES[detectedLanguage] || detectedLanguage.toUpperCase()}
                    </Badge>
                  )}
                </div>
                <div className="relative">
                  <Textarea
                    placeholder="Enter text to convert to speech..."
                    value={text}
                    onChange={(e) => handleTextChange(e.target.value)}
                    className="min-h-[120px] resize-none border-gray-300 focus:border-blue-500 focus:ring-blue-500 bg-white text-gray-900 placeholder:text-gray-500"
                    maxLength={5000}
                  />
                </div>
              </div>
            </Card>

            {/* Voice and Settings Card */}
            <Card className="bg-white border border-gray-200 shadow-sm p-6">
              <div className="space-y-6">
                <div>
                  <label className="text-sm font-medium mb-2 block text-gray-700">Voice</label>
                  <Select value={selectedVoice} onValueChange={setSelectedVoice}>
                    <SelectTrigger className="border-gray-300 focus:border-blue-500 focus:ring-blue-500 bg-white text-gray-900">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-white border-gray-200">
                      {VOICES.map((voice) => (
                        <SelectItem key={voice.id} value={voice.id} className="text-gray-900 hover:bg-blue-600 hover:text-white focus:bg-blue-600 focus:text-white">
                          {voice.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block text-gray-700">Emotion</label>
                  <Select value={selectedEmotion} onValueChange={setSelectedEmotion}>
                    <SelectTrigger className="border-gray-300 focus:border-blue-500 focus:ring-blue-500 bg-white text-gray-900">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-white border-gray-200">
                      <SelectItem value="auto" className="text-gray-900 hover:bg-blue-600 hover:text-white focus:bg-blue-600 focus:text-white">🤖 Auto (自动检测)</SelectItem>
                      <SelectItem value="neutral" className="text-gray-900 hover:bg-blue-600 hover:text-white focus:bg-blue-600 focus:text-white">😐 Neutral (中性)</SelectItem>
                      <SelectItem value="happy" className="text-gray-900 hover:bg-blue-600 hover:text-white focus:bg-blue-600 focus:text-white">😊 Happy (开心)</SelectItem>
                      <SelectItem value="sad" className="text-gray-900 hover:bg-blue-600 hover:text-white focus:bg-blue-600 focus:text-white">😢 Sad (悲伤)</SelectItem>
                      <SelectItem value="angry" className="text-gray-900 hover:bg-blue-600 hover:text-white focus:bg-blue-600 focus:text-white">😠 Angry (愤怒)</SelectItem>
                      <SelectItem value="fearful" className="text-gray-900 hover:bg-blue-600 hover:text-white focus:bg-blue-600 focus:text-white">😨 Fearful (恐惧)</SelectItem>
                      <SelectItem value="disgusted" className="text-gray-900 hover:bg-blue-600 hover:text-white focus:bg-blue-600 focus:text-white">🤢 Disgusted (厌恶)</SelectItem>
                      <SelectItem value="surprised" className="text-gray-900 hover:bg-blue-600 hover:text-white focus:bg-blue-600 focus:text-white">😲 Surprised (惊讶)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block text-gray-700">Speed: {speed[0]}x</label>
                  <Slider value={speed} onValueChange={setSpeed} min={0.5} max={1.5} step={0.1} className="w-full" />
                </div>

                <div>
                  <label className="text-sm font-medium mb-2 block text-gray-700">
                    Pitch: {pitch[0] > 0 ? "+" : ""}
                    {pitch[0]}
                  </label>
                  <Slider value={pitch} onValueChange={setPitch} min={-4} max={4} step={1} className="w-full" />
                </div>

                <div className="flex gap-2">
                  <Button 
                    onClick={handlePlayPause} 
                    disabled={!audioUrl} 
                    variant="outline" 
                    className="flex-1 border-gray-300 hover:border-blue-500 hover:text-blue-600"
                  >
                    {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                  </Button>
                  <Button 
                    onClick={handleDownload} 
                    disabled={!audioUrl} 
                    variant="outline" 
                    className="flex-1 border-gray-300 hover:border-blue-500 hover:text-blue-600"
                  >
                    <Download className="h-4 w-4" />
                  </Button>
                </div>

                <Button
                  onClick={handleSynthesize}
                  disabled={!text.trim() || isLoading}
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                >
                  {isLoading ? "Synthesizing..." : "Generate Speech"}
                </Button>
              </div>
            </Card>
          </div>
        </div>
      </div>

      {/* Audio Element */}
      <audio
        ref={audioRef}
        onEnded={() => setIsPlaying(false)}
        onPlay={() => setIsPlaying(true)}
        onPause={() => setIsPlaying(false)}
      />

      {/* Error Toast */}
      <ErrorToast message={error || ""} isVisible={!!error} onClose={() => setError(null)} />

      {/* Result Modal */}
      <ResultModal
        isOpen={showResultModal}
        onClose={() => setShowResultModal(false)}
        audioUrl={audioUrl || ""}
        fileSize={audioFileSize}
        onCopyLink={handleCopyLink}
        onGenerateAnother={handleGenerateAnother}
      />
    </div>
  )
}
