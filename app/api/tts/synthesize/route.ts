import { NextRequest, NextResponse } from "next/server"
import Replicate from "replicate"

// 验证API Token
if (!process.env.REPLICATE_API_TOKEN) {
  console.error("REPLICATE_API_TOKEN is not set in environment variables")
}

const replicate = new Replicate({
  auth: process.env.REPLICATE_API_TOKEN!,
})

export async function POST(req: NextRequest) {
  try {
    const { text, language = "zh-cn", voiceId, emotion = "neutral", speed = 1.0 } = await req.json()

    // 输入验证
    if (!text || typeof text !== 'string') {
      return NextResponse.json({ error: "Valid text is required" }, { status: 400 })
    }

    if (text.length > 5000) {
      return NextResponse.json({ error: "Text too long (max 5000 characters)" }, { status: 400 })
    }

    if (!process.env.REPLICATE_API_TOKEN) {
      return NextResponse.json({ error: "API configuration error" }, { status: 500 })
    }

    // minimax/speech-02-turbo 支持的语言映射
    const languageMapping: Record<string, string> = {
      "zh-cn": "Chinese",
      "zh": "Chinese",
      "en": "English",
      "ja": "Japanese",
      "ko": "Korean",
      "es": "Spanish",
      "fr": "French",
      "de": "German",
      "it": "Italian",
      "pt": "Portuguese",
      "ru": "Russian"
    }

    // 支持的语音ID映射 - 直接使用sampleUrl作为voice_id
    const getVoiceId = (voiceId: string) => {
      // 从VOICES数组中查找对应的sampleUrl
      const voice = voiceId; // 前端会直接传递voice的id

      // 如果没有找到，使用默认语音
      return voice || "English_ManWithDeepVoice";
    }

    // 支持的情感 (根据minimax/speech-02-turbo实际支持的情感)
    const emotionMapping: Record<string, string> = {
      "auto": "auto",           // 自动检测情感
      "neutral": "neutral",     // 中性
      "happy": "happy",         // 开心
      "sad": "sad",            // 悲伤
      "angry": "angry",        // 愤怒
      "fearful": "fearful",    // 恐惧
      "disgusted": "disgusted", // 厌恶
      "surprised": "surprised", // 惊讶
      // 兼容旧的映射
      "excited": "surprised",   // excited映射到surprised
      "calm": "neutral"         // calm映射到neutral
    }

    const mappedLanguage = languageMapping[language] || "English"
    const mappedVoiceId = getVoiceId(voiceId)
    const mappedEmotion = emotionMapping[emotion] || "neutral"

    const input = {
      text: text.trim(),
      voice_id: mappedVoiceId,
      emotion: mappedEmotion,
      language_boost: mappedLanguage,
      english_normalization: mappedLanguage === "English"
    }

    console.log("Final input to Replicate:", input)

    const output = await replicate.run("minimax/speech-02-turbo", { input })

    console.log("Output from Replicate:", typeof output, output instanceof Buffer ? "Buffer" : output instanceof ReadableStream ? "ReadableStream" : "Other")

    // minimax/speech-02-turbo 可能返回不同格式的数据
    let url: string | null = null

    if (output instanceof Buffer) {
      // 直接处理Buffer数据
      const base64 = output.toString('base64')
      url = `data:audio/mp3;base64,${base64}`
      console.log("Converted Buffer to data URL, size:", output.length)
    } else if (Array.isArray(output) && output[0] instanceof Buffer) {
      // 如果是Buffer数组
      const base64 = output[0].toString('base64')
      url = `data:audio/mp3;base64,${base64}`
      console.log("Converted Buffer array to data URL, size:", output[0].length)
    } else if (output instanceof ReadableStream) {
      // 处理ReadableStream
      console.log("Processing ReadableStream from minimax")
      try {
        const response = new Response(output)
        const arrayBuffer = await response.arrayBuffer()
        const buffer = Buffer.from(arrayBuffer)
        const base64 = buffer.toString('base64')
        url = `data:audio/mp3;base64,${base64}`
        console.log("Converted ReadableStream to data URL, size:", buffer.length)
      } catch (streamError) {
        console.error("Failed to process ReadableStream:", streamError)
        return NextResponse.json({
          error: "Failed to process audio stream from Replicate."
        }, { status: 500 })
      }
    } else if (typeof output === 'string') {
      // 如果是URL字符串
      url = output
    } else if (output && typeof output === 'object' && 'url' in output) {
      // 如果是包含URL的对象
      url = (output as any).url
    } else {
      console.error("Unexpected output format:", output)
      return NextResponse.json({ error: "Unexpected output format from Replicate." }, { status: 500 })
    }

    if (!url || typeof url !== 'string') {
      console.error("Failed to process output:", output)
      return NextResponse.json({ error: "Failed to get valid audio data from Replicate." }, { status: 500 })
    }

    return NextResponse.json({ url })
  } catch (err: any) {
    console.error("Replicate error →", err.message)

    // 更详细的错误处理
    if (err.message.includes("authentication")) {
      return NextResponse.json({ error: "API authentication failed. Please check your API token." }, { status: 401 })
    } else if (err.message.includes("quota") || err.message.includes("limit")) {
      return NextResponse.json({ error: "API quota exceeded. Please try again later." }, { status: 429 })
    } else if (err.message.includes("timeout")) {
      return NextResponse.json({ error: "Request timeout. Please try again." }, { status: 408 })
    }

    return NextResponse.json({ error: err.message || "An unexpected error occurred" }, { status: 500 })
  }
}
